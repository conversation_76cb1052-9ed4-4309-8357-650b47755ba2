package com.gianghp.hrm_hr_service.controllers;


import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/health")
@RequiredArgsConstructor

public class HealthController {
  @GetMapping
  public ResponseEntity health(@RequestHeader HttpHeaders headers) {
    return ResponseEntity.ok().headers(headers).body("HR OK");
  }
}
