package com.gianghp.hrm_auth_service.controllers;

import com.gianghp.hrm.dtos.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/health")
@RequiredArgsConstructor
public class HealthController {

  @GetMapping
  public ResponseEntity<ApiResponse> health() {
    HttpHeaders headers = new HttpHeaders();
    headers.add("X-Auth-Service-Status", "OK");
    headers.add("X-Checked-By", "auth-service");

    return ResponseEntity
        .ok()
        .headers(headers)
        .body(ApiResponse.success("OK"));
  }
}
