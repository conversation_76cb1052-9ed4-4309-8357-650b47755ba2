# Test configuration
spring.application.name=hrm-auth-service-test

# Use H2 in-memory database for tests
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# JPA/Hibernate configuration for tests
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# Disable DataInitializer for tests
spring.autoconfigure.exclude=com.gianghp.hrm_auth_service.configs.DataInitializer

# Logging
logging.level.com.gianghp.hrm_auth_service=INFO
logging.level.org.springframework.security=WARN
logging.level.org.hibernate=WARN
